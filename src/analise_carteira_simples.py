#!/usr/bin/env python3
"""
Script simplificado para análise de carteira - ideal para carteiras novas
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yfinance as yf
from datetime import datetime
import os

# Configuração de estilo
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def corrigir_valores_tims3(dados, ticker):
    """
    Corrige valores da TIMS3 até 02/07/2025 dividindo por 100

    Args:
        dados: DataFrame com dados históricos
        ticker: Símbolo da ação

    Returns:
        DataFrame com valores corrigidos
    """
    if 'TIMS3' not in ticker:
        return dados

    # Data limite para correção (sem timezone)
    data_limite = pd.Timestamp('2025-07-02').tz_localize(None)

    # Colunas OHLC e Volume para corrigir
    colunas_preco = ['Open', 'High', 'Low', 'Close']

    # Converter índice para timezone naive se necessário
    if dados.index.tz is not None:
        dados_index = dados.index.tz_localize(None)
    else:
        dados_index = dados.index

    # Filtrar dados até a data limite
    mask_correcao = dados_index <= data_limite

    if mask_correcao.any():
        print(f"     🔧 Corrigindo valores TIMS3 até 02/07/2025 (dividindo por 100)")

        # Aplicar correção nas colunas de preço
        for coluna in colunas_preco:
            if coluna in dados.columns:
                dados.loc[mask_correcao, coluna] = dados.loc[mask_correcao, coluna] / 100

        print(f"     ✅ Correção aplicada em {mask_correcao.sum()} registros")

    return dados

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    try:
        carteira = pd.read_csv(arquivo_csv)
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
        return carteira
    except Exception as e:
        print(f"Erro ao carregar carteira: {e}")
        return None

def obter_precos_atuais(tickers):
    """Obtém preços atuais das ações, usando o close mais recente se o valor do dia atual não estiver disponível"""
    print("Obtendo preços atuais...")
    precos = {}

    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            # Primeiro tenta obter dados do dia atual
            hist = stock.history(period='1d')

            if not hist.empty:
                # Aplicar correção para TIMS3 se necessário
                hist = corrigir_valores_tims3(hist, ticker)
                precos[ticker] = hist['Close'].iloc[-1]
                print(f"✓ Preço obtido para {ticker} (dia atual): R$ {precos[ticker]:.2f}")
            else:
                # Se não há dados do dia atual, busca dados dos últimos 5 dias
                print(f"⚠️  Dados do dia atual não disponíveis para {ticker}, buscando dados mais recentes...")
                hist = stock.history(period='5d')

                if not hist.empty:
                    # Aplicar correção para TIMS3 se necessário
                    hist = corrigir_valores_tims3(hist, ticker)
                    precos[ticker] = hist['Close'].iloc[-1]
                    data_mais_recente = hist.index[-1].strftime('%d/%m/%Y')
                    print(f"✓ Preço mais recente obtido para {ticker} ({data_mais_recente}): R$ {precos[ticker]:.2f}")
                else:
                    print(f"✗ Nenhum preço encontrado para {ticker} nos últimos 5 dias")
        except Exception as e:
            print(f"✗ Erro ao obter preço para {ticker}: {e}")

    return precos

def consolidar_posicoes_simples(carteira):
    """Consolida posições do mesmo ticker, tratando valores negativos como vendas"""
    posicoes_consolidadas = {}
    print(carteira)
    for _, posicao in carteira.iterrows():
        ticker = posicao['ticker']
        quantidade = posicao['quantidade']
        data_compra = posicao['data_compra']
        preco_compra = posicao['preco_compra']

        # Determinar se é compra ou venda
        eh_venda = quantidade < 0
        valor_transacao = abs(quantidade) * preco_compra

        if ticker not in posicoes_consolidadas:
            # Primeira ocorrência do ticker
            if eh_venda:
                print(f"⚠️  Aviso: Tentativa de venda de {ticker} sem posição prévia. Ignorando transação.")
                continue

            posicoes_consolidadas[ticker] = {
                'ticker': ticker,
                'quantidade_total': quantidade,
                'valor_investido_total': valor_transacao,
                'valor_investido_original': valor_transacao,  # Rastrear valor original
                'valor_vendido_total': 0.0,
                'data_primeira_compra': data_compra,
                'preco_medio_compra': preco_compra
            }
        else:
            # Ticker já existe, consolidar
            posicoes_consolidadas[ticker]['quantidade_total'] += quantidade

            if eh_venda:
                # É uma venda - subtrair do valor investido e adicionar ao valor vendido
                posicoes_consolidadas[ticker]['valor_investido_total'] -= valor_transacao
                posicoes_consolidadas[ticker]['valor_vendido_total'] += valor_transacao
            else:
                # É uma compra - adicionar ao valor investido e ao valor original
                posicoes_consolidadas[ticker]['valor_investido_total'] += valor_transacao
                posicoes_consolidadas[ticker]['valor_investido_original'] += valor_transacao
                # Atualizar data da primeira compra se necessário
                if data_compra < posicoes_consolidadas[ticker]['data_primeira_compra']:
                    posicoes_consolidadas[ticker]['data_primeira_compra'] = data_compra

            # Recalcular preço médio de compra baseado apenas nas compras
            if posicoes_consolidadas[ticker]['quantidade_total'] > 0:
                # Calcular preço médio baseado no valor investido total e quantidade atual
                total_compras = posicoes_consolidadas[ticker]['valor_investido_total']
                qtd_atual = posicoes_consolidadas[ticker]['quantidade_total']
                posicoes_consolidadas[ticker]['preco_medio_compra'] = total_compras / qtd_atual

    return posicoes_consolidadas

def calcular_rendimentos(carteira, precos_atuais):
    """Calcula rendimentos da carteira consolidando posições por ticker"""
    # Primeiro consolidar posições
    posicoes_consolidadas = consolidar_posicoes_simples(carteira)

    resultados = []

    for ticker, dados in posicoes_consolidadas.items():
        if ticker in precos_atuais:
            preco_atual = precos_atuais[ticker]

            quantidade_total = dados['quantidade_total']
            valor_investido_total = dados['valor_investido_total']
            valor_investido_original = dados['valor_investido_original']
            valor_vendido_total = dados['valor_vendido_total']
            preco_medio_compra = dados['preco_medio_compra']
            data_primeira_compra = dados['data_primeira_compra']

            # Cálculos
            valor_atual = quantidade_total * preco_atual if quantidade_total > 0 else 0
            # Valor total recuperável = valor atual + valor das vendas
            valor_total_recuperavel = valor_atual + valor_vendido_total
            # Rendimento = valor total recuperável - valor investido original
            rendimento_absoluto = valor_total_recuperavel - valor_investido_original
            rendimento_percentual = (rendimento_absoluto / valor_investido_original) * 100 if valor_investido_original > 0 else 0

            resultado = {
                'ticker': ticker,
                'quantidade': quantidade_total,
                'data_compra': data_primeira_compra,
                'preco_compra': preco_medio_compra,
                'preco_atual': preco_atual,
                'valor_investido': valor_investido_original,  # Valor investido bruto
                'valor_atual': valor_atual,  # Valor atual das posições
                'valor_total_recuperavel': valor_total_recuperavel,  # Valor atual + vendas
                'valor_vendido_total': valor_vendido_total,  # Valor das vendas
                'rendimento_absoluto': rendimento_absoluto,
                'rendimento_percentual': rendimento_percentual
            }

            resultados.append(resultado)

    return resultados

def gerar_graficos_simples(resultados):
    """Gera gráficos simples para carteira"""
    os.makedirs('results/figures', exist_ok=True)
    
    if not resultados:
        print("Nenhum resultado para gerar gráficos")
        return
    print('1', resultados)
    # Preparar dados
    tickers = [r['ticker'] for r in resultados]
    valores_investidos = [r['valor_investido'] for r in resultados]
    valores_atuais = [r['valor_atual'] for r in resultados]
    valores_recuperaveis = [r['valor_total_recuperavel'] for r in resultados]
    rendimentos_pct = [r['rendimento_percentual'] for r in resultados]
    valores_vendidos = [r['valor_vendido_total'] for r in resultados]

    # Calcular totais para o gráfico adicional (correspondente ao terceiro gráfico do script completo)
    valor_total_investido_bruto = sum(valores_investidos)  # Valor investido bruto (sem subtração das vendas)
    valor_total_recuperavel = sum(valores_recuperaveis)    # Valor atual + vendas

    # Criar figura com 4 subplots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Gráfico de barras - Valores Investidos vs Atuais
    x = np.arange(len(tickers))
    width = 0.35
    
    ax1.bar(x - width/2, valores_investidos, width, label='Valor Investido', alpha=0.8)
    ax1.bar(x + width/2, valores_atuais, width, label='Valor Atual', alpha=0.8)
    ax1.set_xlabel('Ações')
    ax1.set_ylabel('Valor (R$)')
    ax1.set_title('Comparação: Valor Investido vs Atual')
    ax1.set_xticks(x)
    ax1.set_xticklabels([t.replace('.SA', '') for t in tickers])
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Gráfico de pizza - Distribuição da carteira
    ax2.pie(valores_atuais, labels=[t.replace('.SA', '') for t in tickers], autopct='%1.1f%%', startangle=90)
    ax2.set_title('Distribuição da Carteira (Valor Atual)')
    
    # 3. Gráfico de barras - Rendimento percentual
    colors = ['green' if r >= 0 else 'red' for r in rendimentos_pct]
    ax3.bar(range(len(tickers)), rendimentos_pct, color=colors, alpha=0.7)
    ax3.set_xlabel('Ações')
    ax3.set_ylabel('Rendimento (%)')
    ax3.set_title('Rendimento por Ação')
    ax3.set_xticks(range(len(tickers)))
    ax3.set_xticklabels([t.replace('.SA', '') for t in tickers])
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax3.grid(True, alpha=0.3)
    
    # Adicionar valores nas barras do rendimento
    for i, v in enumerate(rendimentos_pct):
        ax3.text(i, v + (0.1 if v >= 0 else -0.1), f'{v:.2f}%',
                ha='center', va='bottom' if v >= 0 else 'top')

    # 4. Gráfico de barras - Capital Total (correspondente ao terceiro gráfico do script completo)
    capital_inicial = 1000.0
    capital_disponivel = capital_inicial - valor_total_investido_bruto + sum(valores_vendidos)
    capital_total = capital_disponivel + valor_total_recuperavel - sum(valores_vendidos)

    categorias = ['Capital Inicial', 'Capital Total']
    valores_totais = [capital_inicial, capital_total]
    cores = ['#2E8B57', '#4169E1']  # Mesmas cores do terceiro gráfico

    bars = ax4.bar(categorias, valores_totais, color=cores, alpha=0.8, edgecolor='black', linewidth=1)
    ax4.set_ylabel('Valor (R$)')
    ax4.set_title(f'Evolução do Capital Total - R$ {capital_inicial:.0f} Iniciais')
    ax4.grid(True, alpha=0.3, axis='y')

    # Adicionar valores nas barras
    for bar, valor in zip(bars, valores_totais):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + valor*0.01,
                f'R$ {valor:.2f}', ha='center', va='bottom', fontweight='bold')

    # Calcular rendimento como (Capital Total / Capital Inicial) - 1
    diferenca = capital_total - capital_inicial
    rendimento_capital_pct = ((capital_total / capital_inicial) - 1) * 100

    if abs(diferenca) > 0.01:  # Se diferença for significativa
        cor_diferenca = 'green' if diferenca > 0 else 'red'
        simbolo = '+' if diferenca > 0 else ''
        ax4.text(0.5, max(valores_totais) * 0.9,
                f'Rendimento: {simbolo}R$ {diferenca:.2f}\n({simbolo}{rendimento_capital_pct:.2f}%)',
                ha='center', va='center', fontsize=12, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor=cor_diferenca, alpha=0.2))

        # Adicionar informações adicionais
        ax4.text(0.5, max(valores_totais) * 0.7,
                f'Capital Disponível: R$ {capital_disponivel:.2f}\nValor do Portfólio: R$ {valor_total_recuperavel- sum(valores_vendidos):.2f}',
                ha='center', va='center', fontsize=10,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.3))

    plt.tight_layout()
    plt.savefig('results/figures/analise_carteira_simples.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("Gráfico salvo em 'results/figures/analise_carteira_simples.png'")

def gerar_relatorio(resultados):
    """Gera relatório resumo"""
    if not resultados:
        print("Nenhum resultado para gerar relatório")
        return
    
    # Calcular totais (correspondente ao terceiro gráfico do script completo)
    valor_total_investido_bruto = sum(r['valor_investido'] for r in resultados)  # Valor investido bruto
    valor_total_recuperavel = sum(r['valor_total_recuperavel'] for r in resultados)  # Valor atual + vendas

    # Calcular capital total (lógica do terceiro gráfico)
    capital_inicial = 1000.0
    capital_disponivel = capital_inicial - valor_total_investido_bruto
    capital_total = capital_disponivel + valor_total_recuperavel

    # Rendimento baseado no capital total
    rendimento_capital_absoluto = capital_total - capital_inicial
    rendimento_capital_percentual = ((capital_total / capital_inicial) - 1) * 100
    
    print("\n" + "="*60)
    print("ANÁLISE SIMPLIFICADA DA CARTEIRA")
    print("="*60)
    
    print(f"\nDATA DA ANÁLISE: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
    
    print(f"\nRESUMO GERAL:")
    print(f"Capital Inicial: R$ {capital_inicial:.2f}")
    print(f"Valor Total Investido (Bruto): R$ {valor_total_investido_bruto:.2f}")
    print(f"Capital Disponível: R$ {capital_disponivel:.2f}")
    print(f"Valor Total Recuperável (Atual + Vendas): R$ {valor_total_recuperavel:.2f}")
    print(f"Capital Total: R$ {capital_total:.2f}")
    print(f"Rendimento do Capital: R$ {rendimento_capital_absoluto:.2f} ({rendimento_capital_percentual:.2f}%)")

    # Status da carteira baseado no rendimento do capital
    if rendimento_capital_percentual > 0:
        status = "📈 LUCRO"
    elif rendimento_capital_percentual < 0:
        status = "📉 PREJUÍZO"
    else:
        status = "➡️ NEUTRO"
    
    print(f"Status: {status}")
    
    print(f"\nDETALHE POR AÇÃO:")
    print("-" * 80)
    for resultado in resultados:
        ticker_clean = resultado['ticker'].replace('.SA', '')
        status_acao = "📈" if resultado['rendimento_percentual'] > 0 else "📉" if resultado['rendimento_percentual'] < 0 else "➡️"
        
        print(f"\n{status_acao} {ticker_clean}:")
        print(f"  Quantidade: {resultado['quantidade']} ações")
        print(f"  Data da Compra: {resultado['data_compra'].strftime('%d/%m/%Y')}")
        print(f"  Preço de Compra: R$ {resultado['preco_compra']:.2f}")
        print(f"  Preço Atual: R$ {resultado['preco_atual']:.2f}")
        print(f"  Valor Investido: R$ {resultado['valor_investido']:.2f}")
        print(f"  Valor Atual: R$ {resultado['valor_atual']:.2f}")
        print(f"  Rendimento: R$ {resultado['rendimento_absoluto']:.2f} ({resultado['rendimento_percentual']:.2f}%)")
        
        # Participação na carteira baseada no valor total recuperável
        participacao = (resultado['valor_total_recuperavel'] / valor_total_recuperavel) * 100
        print(f"  Participação na Carteira: {participacao:.1f}%")
        print(f"  Valor Total Recuperável: R$ {resultado['valor_total_recuperavel']:.2f}")
        if resultado['valor_vendido_total'] > 0:
            print(f"  Valor Vendido: R$ {resultado['valor_vendido_total']:.2f}")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'
    
    print("Iniciando análise simplificada da carteira...")
    
    # Carregar carteira
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None:
        return
    
    print(f"Carteira carregada: {len(carteira)} posições")
    
    # Obter preços atuais
    tickers = carteira['ticker'].unique()
    precos_atuais = obter_precos_atuais(tickers)
    
    if not precos_atuais:
        print("Nenhum preço atual foi obtido. Verifique os tickers.")
        return
    
    # Calcular rendimentos
    resultados = calcular_rendimentos(carteira, precos_atuais)
    
    if not resultados:
        print("Nenhum resultado calculado.")
        return
    
    # Gerar gráficos
    gerar_graficos_simples(resultados)
    
    # Gerar relatório
    gerar_relatorio(resultados)
    
    # Salvar resultados em CSV
    os.makedirs('results', exist_ok=True)
    df_resultados = pd.DataFrame(resultados)
    df_resultados.to_csv('results/analise_carteira_simples.csv', index=False)
    print(f"\nResultados salvos em 'results/analise_carteira_simples.csv'")

if __name__ == "__main__":
    main()
