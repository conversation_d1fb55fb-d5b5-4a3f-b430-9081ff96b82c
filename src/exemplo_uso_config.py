#!/usr/bin/env python3
"""
Exemplo de como usar o sistema de configuração centralizada
Este script demonstra como adaptar os scripts existentes para usar config.yaml
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import config, setup_environment
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def exemplo_configuracao_basica():
    """
    Exemplo básico de uso das configurações
    """
    print("🔧 EXEMPLO DE USO DA CONFIGURAÇÃO CENTRALIZADA")
    print("=" * 60)
    
    # Configurar ambiente automaticamente
    setup_environment()
    
    # Obter configurações básicas
    print("\n📋 Configurações Gerais:")
    print(f"   • Backend matplotlib: {config.get('general.matplotlib_backend')}")
    print(f"   • Suprimir warnings: {config.get('general.suppress_warnings')}")
    print(f"   • Seed aleatório: {config.get('general.random_seed')}")
    
    # Obter configurações de dados
    print("\n📊 Configurações de Dados:")
    file_paths = config.get_file_paths()
    for name, path in file_paths.items():
        print(f"   • {name}: {path}")
    
    # Obter configurações de médias móveis
    print("\n📈 Janelas das Médias Móveis:")
    mm_windows = config.get_moving_average_windows()
    for mm_name, window in mm_windows.items():
        print(f"   • {mm_name.upper()}: {window} dias")
    
    # Obter configurações de cores
    print("\n🎨 Configurações de Cores:")
    colors = config.get_colors()
    for element, color in colors.items():
        print(f"   • {element}: {color}")

def exemplo_adaptacao_script_mm():
    """
    Exemplo de como adaptar um script de médias móveis para usar configurações
    """
    print("\n🔄 EXEMPLO DE ADAPTAÇÃO - SCRIPT DE MÉDIAS MÓVEIS")
    print("=" * 60)
    
    # Antes (hardcoded):
    # mm25_window = 25
    # mm200_window = 200
    # color_mm25 = 'green'
    # color_mm200 = 'red'
    
    # Depois (usando configuração):
    mm_windows = config.get_moving_average_windows()
    colors = config.get_colors()
    
    mm25_window = mm_windows.get('mm25', 25)
    mm200_window = mm_windows.get('mm200', 200)
    color_mm25 = colors.get('mm25', 'green')
    color_mm200 = colors.get('mm200', 'red')
    
    print(f"   • MM25: {mm25_window} dias, cor: {color_mm25}")
    print(f"   • MM200: {mm200_window} dias, cor: {color_mm200}")
    
    # Exemplo de uso em gráfico
    print("\n   Exemplo de código adaptado:")
    print(f"   ax.plot(data.index, data['MM25'], color='{color_mm25}', linewidth={config.get('visualization.line_widths.mm25', 2)})")
    print(f"   ax.plot(data.index, data['MM200'], color='{color_mm200}', linewidth={config.get('visualization.line_widths.mm200', 2)})")

def exemplo_adaptacao_script_lstm():
    """
    Exemplo de como adaptar um script LSTM para usar configurações
    """
    print("\n🧠 EXEMPLO DE ADAPTAÇÃO - SCRIPT LSTM")
    print("=" * 60)
    
    # Obter configurações do LSTM
    lstm_config = config.get_lstm_config()
    
    # Antes (hardcoded):
    # LOOKBACK = 20
    # lstm_units = [50, 50]
    # dropout_rate = 0.2
    # epochs = 50
    # batch_size = 32
    
    # Depois (usando configuração):
    lookback = lstm_config.get('lookback_window', 20)
    lstm_units = lstm_config.get('architecture', {}).get('lstm_units', [50, 50])
    dropout_rate = lstm_config.get('architecture', {}).get('dropout_rate', 0.2)
    epochs = lstm_config.get('training', {}).get('epochs', 50)
    batch_size = lstm_config.get('training', {}).get('batch_size', 32)
    features = lstm_config.get('features', ['Close', 'Volume'])
    
    print(f"   • Lookback window: {lookback}")
    print(f"   • LSTM units: {lstm_units}")
    print(f"   • Dropout rate: {dropout_rate}")
    print(f"   • Epochs: {epochs}")
    print(f"   • Batch size: {batch_size}")
    print(f"   • Features: {features}")

def exemplo_adaptacao_script_kalman():
    """
    Exemplo de como adaptar um script Kalman para usar configurações
    """
    print("\n🎯 EXEMPLO DE ADAPTAÇÃO - SCRIPT KALMAN")
    print("=" * 60)
    
    # Obter configurações do Kalman
    kalman_config = config.get_kalman_config()
    
    # Antes (hardcoded):
    # Q = 0.1
    # R = 1.0
    # prediction_days = 20
    
    # Depois (usando configuração):
    process_noise = kalman_config.get('process_noise_covariance', 0.1)
    observation_noise = kalman_config.get('observation_noise_covariance', 1.0)
    prediction_days = kalman_config.get('prediction_days', 20)
    spread_window = kalman_config.get('spread_window', 20)
    
    print(f"   • Process noise (Q): {process_noise}")
    print(f"   • Observation noise (R): {observation_noise}")
    print(f"   • Prediction days: {prediction_days}")
    print(f"   • Spread window: {spread_window}")

def exemplo_adaptacao_script_correlacao():
    """
    Exemplo de como adaptar um script de correlação para usar configurações
    """
    print("\n🔗 EXEMPLO DE ADAPTAÇÃO - SCRIPT CORRELAÇÃO")
    print("=" * 60)
    
    # Obter configurações de correlação
    corr_config = config.get_correlation_config()
    
    # Antes (hardcoded):
    # num_stocks = 40
    # correlation_threshold = 0.9
    # period = '1y'
    
    # Depois (usando configuração):
    num_stocks = corr_config.get('diversification_stocks', 40)
    correlation_threshold = corr_config.get('high_correlation_threshold', 0.9)
    period = config.get('data.periods.correlation_period', '1y')
    prioritize_dividends = corr_config.get('prioritize_dividend_stocks', True)
    
    print(f"   • Número de ações para diversificação: {num_stocks}")
    print(f"   • Threshold de correlação: {correlation_threshold}")
    print(f"   • Período de dados: {period}")
    print(f"   • Priorizar ações com dividendos: {prioritize_dividends}")

def exemplo_criacao_diretorios():
    """
    Exemplo de criação automática de diretórios
    """
    print("\n📁 EXEMPLO DE CRIAÇÃO DE DIRETÓRIOS")
    print("=" * 60)
    
    # Criar diretórios automaticamente
    config.create_output_directories()
    
    # Listar diretórios criados
    directories = config.get_output_directories()
    print("   Diretórios criados:")
    for name, path in directories.items():
        print(f"   • {name}: {path}")

def exemplo_template_script_adaptado():
    """
    Template de como um script adaptado ficaria
    """
    print("\n📝 TEMPLATE DE SCRIPT ADAPTADO")
    print("=" * 60)
    
    template = '''
#!/usr/bin/env python3
"""
Script adaptado para usar configuração centralizada
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import config, setup_environment
import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd

def main():
    # Configurar ambiente automaticamente
    setup_environment()
    
    # Obter configurações
    mm_windows = config.get_moving_average_windows()
    colors = config.get_colors()
    file_paths = config.get_file_paths()
    
    # Usar configurações no código
    carteira_file = file_paths.get('carteira', 'carteira.csv')
    mm25_window = mm_windows.get('mm25', 25)
    mm200_window = mm_windows.get('mm200', 200)
    
    # Resto do código...
    print(f"Carregando carteira de: {carteira_file}")
    print(f"Usando MM25 de {mm25_window} dias")
    print(f"Usando MM200 de {mm200_window} dias")

if __name__ == "__main__":
    main()
'''
    
    print("   Template salvo em: exemplo_template_adaptado.py")
    print("   Use este template como base para adaptar outros scripts")

def main():
    """
    Função principal - executa todos os exemplos
    """
    exemplo_configuracao_basica()
    exemplo_adaptacao_script_mm()
    exemplo_adaptacao_script_lstm()
    exemplo_adaptacao_script_kalman()
    exemplo_adaptacao_script_correlacao()
    exemplo_criacao_diretorios()
    exemplo_template_script_adaptado()
    
    print("\n✅ EXEMPLOS CONCLUÍDOS!")
    print("=" * 60)
    print("📚 Para adaptar seus scripts:")
    print("   1. Importe: from config_loader import config, setup_environment")
    print("   2. Chame: setup_environment() no início do script")
    print("   3. Substitua valores hardcoded por: config.get('caminho.da.configuracao')")
    print("   4. Use as funções de conveniência para configurações comuns")
    print("\n🔧 Para modificar parâmetros:")
    print("   • Edite o arquivo config.yaml na raiz do projeto")
    print("   • Todos os scripts usarão automaticamente os novos valores")

if __name__ == "__main__":
    main()
